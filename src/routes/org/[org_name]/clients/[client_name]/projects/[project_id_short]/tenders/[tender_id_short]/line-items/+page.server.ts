import type { Actions, PageServerLoad } from './$types';
import { requireUser, requireProject } from '$lib/server/auth';
import { redirect } from 'sveltekit-flash-message/server';
import { error, fail } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { projectUUID, tenderUUID } from '$lib/schemas/project';
import { createLineItemSchema, editLineItemSchema } from '$lib/schemas/tender';
import { getTenderById, createLineItem, updateLineItem, deleteLineItem } from '$lib/tender_utils';
import { message } from 'sveltekit-superforms';

export const load: PageServerLoad = async ({ locals, params }) => {
	await requireUser();
	const { supabase } = locals;
	const { project_id_short } = requireProject();
	const { tender_id_short } = params;

	if (!tender_id_short) {
		throw error(400, 'Tender ID is required');
	}

	const project_id = projectUUID(project_id_short);
	const tender_id = tenderUUID(tender_id_short);

	// Fetch tender details with line items
	const tender = await getTenderById(supabase, tender_id);

	// Verify the tender belongs to the project
	if (tender.project_id !== project_id) {
		throw error(404, 'Tender not found');
	}

	// Initialize forms for creating and editing line items
	const form = await superValidate(zod(createLineItemSchema));
	const editForm = await superValidate(zod(editLineItemSchema));

	return {
		tender,
		tender_id_short,
		form,
		editForm,
	};
};

export const actions: Actions = {
	create: async ({ request, locals, params }) => {
		await requireUser();
		const { supabase } = locals;
		const { tender_id_short } = params;

		if (!tender_id_short) {
			throw error(400, 'Tender ID is required');
		}

		const tender_id = tenderUUID(tender_id_short);

		// Validate form data with superforms
		const form = await superValidate(request, zod(createLineItemSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		try {
			// TODO: turn this into an RPC
			// Get current revision
			const { data: currentRevision, error: revisionError } = await supabase
				.from('tender_revision')
				.select('tender_revision_id')
				.eq('tender_id', tender_id)
				.eq('is_current', true)
				.single();

			if (revisionError || !currentRevision) {
				return fail(500, {
					form,
					message: { type: 'error', text: 'Failed to find current tender revision' },
				});
			}

			const lineItemData = {
				tender_revision_id: currentRevision.tender_revision_id,
				...form.data,
			};

			await createLineItem(supabase, lineItemData);
		} catch (err) {
			if (err instanceof Response) {
				throw err;
			}
			locals.log.error({ msg: 'Error creating line item:', err });
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to create line item' },
			});
		}
		return message(form, {
			type: 'success',
			text: `Line item has been added successfully.`,
		});
	},

	update: async ({ request, locals, params }) => {
		await requireUser();
		const { supabase } = locals;
		const { tender_id_short } = params;

		if (!tender_id_short) {
			throw error(400, 'Tender ID is required');
		}

		// Validate form data with superforms
		const form = await superValidate(request, zod(editLineItemSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		const lineItemId = form.data.tender_line_item_id;

		if (!lineItemId) {
			return fail(400, {
				form,
				message: { type: 'error', text: 'Line item ID is required' },
			});
		}

		try {
			await updateLineItem(supabase, lineItemId, form.data);
		} catch (err) {
			if (err instanceof Response) {
				throw err;
			}
			locals.log.error({ msg: 'Error updating line item:', err });
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to update line item' },
			});
		}
		return message(form, {
			type: 'success',
			text: `Line item has been updated successfully.`,
		});
	},

	delete: async ({ request, locals, cookies, params }) => {
		await requireUser();
		const { supabase } = locals;
		const { org_name, client_name, project_id_short } = requireProject();
		const { tender_id_short } = params;

		if (!tender_id_short) {
			throw error(400, 'Tender ID is required');
		}

		const formData = await request.formData();
		const lineItemId = formData.get('line_item_id') as string;

		if (!lineItemId) {
			return fail(400, {
				message: { type: 'error', text: 'Line item ID is required' },
			});
		}

		try {
			await deleteLineItem(supabase, lineItemId);

			throw redirect(
				302,
				`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(
					client_name,
				)}/projects/${encodeURIComponent(project_id_short)}/tenders/${encodeURIComponent(
					tender_id_short,
				)}/line-items`,
				{
					type: 'success',
					message: 'Line item has been deleted successfully.',
				},
				cookies,
			);
		} catch (err) {
			if (err instanceof Response) {
				throw err;
			}
			locals.log.error({ msg: 'Error deleting line item:', err });
			return fail(500, {
				message: { type: 'error', text: 'Failed to delete line item' },
			});
		}
	},
};
