import type { Actions, PageServerLoad } from './$types';
import { requireUser, requireProject } from '$lib/server/auth';
import { redirect } from 'sveltekit-flash-message/server';
import { fail } from '@sveltejs/kit';
import { projectUUID } from '$lib/schemas/project';
import { getProjectTenders, deleteTender } from '$lib/tender_utils';

export const load: PageServerLoad = async ({ locals, depends }) => {
	depends('project:tenders');
	await requireUser();
	const { supabase } = locals;
	const { project_id_short } = requireProject();

	const project_id = projectUUID(project_id_short);

	// Fetch tenders using the RPC function
	const tenders = await getProjectTenders(supabase, project_id);

	return {
		tenders: tenders || [],
	};
};

export const actions: Actions = {
	delete: async ({ request, locals, cookies }) => {
		await requireUser();
		const { supabase } = locals;
		const { org_name, client_name, project_id_short } = requireProject();

		const formData = await request.formData();
		const tenderId = formData.get('tender_id') as string;

		if (!tenderId) {
			return fail(400, {
				message: { type: 'error', text: 'Tender ID is required' },
			});
		}

		let tender_name: string | null = null;
		try {
			// Get tender name for success message
			const { data: tenderData, error: tenderError } = await supabase
				.from('tender')
				.select('tender_name')
				.eq('tender_id', tenderId)
				.single();

			if (tenderError) {
				return fail(500, {
					message: { type: 'error', text: 'Failed to find tender' },
				});
			}

			tender_name = tenderData?.tender_name || null;

			// Delete the tender
			await deleteTender(supabase, tenderId);
		} catch (err) {
			if (err instanceof Response) {
				throw err;
			}
			return fail(500, {
				message: { type: 'error', text: 'Failed to delete tender' },
			});
		}

		throw redirect(
			302,
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(
				client_name,
			)}/projects/${encodeURIComponent(project_id_short)}/tenders`,
			{
				type: 'success',
				message: `Tender "${tender_name}" has been deleted successfully.`,
			},
			cookies,
		);
	},
};
