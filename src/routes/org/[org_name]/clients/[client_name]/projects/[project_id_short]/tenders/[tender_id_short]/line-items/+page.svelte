<script lang="ts">
	import type { PageProps } from './$types';
	import { Button } from '$lib/components/ui/button';
	import * as Card from '$lib/components/ui/card';
	import * as Dialog from '$lib/components/ui/dialog';
	import * as Form from '$lib/components/ui/form';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow,
	} from '$lib/components/ui/table';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import PlusIcon from 'phosphor-svelte/lib/Plus';
	import TrashIcon from 'phosphor-svelte/lib/Trash';
	import DotsThreeVerticalIcon from 'phosphor-svelte/lib/DotsThreeVertical';
	import PencilIcon from 'phosphor-svelte/lib/Pencil';
	import { superForm } from 'sveltekit-superforms';
	import { zod4Client as zodClient } from 'sveltekit-superforms/adapters';
	import { createLineItemSchema, editLineItemSchema } from '$lib/schemas/tender';
	import { toast } from 'svelte-sonner';
	import { enhance as svelteKitEnhance } from '$app/forms';
	import ConfirmationDialog from '$lib/components/tender/ConfirmationDialog.svelte';
	import NormalizationInput from '$lib/components/tender/NormalizationInput.svelte';
	import { formatCurrency } from '$lib/utils';
	const { data }: PageProps = $props();

	const tender = $derived(data.tender);
	const lineItems = $derived(tender?.tender_revision?.[0]?.tender_line_item || []);

	let addDialogOpen = $state(false);
	let editDialogOpen = $state(false);
	let deleteDialogOpen = $state(false);
	let selectedLineItem = $state<(typeof lineItems)[0] | null>(null);

	// Initialize superforms for create and edit
	const form = superForm(data.form, {
		validators: zodClient(createLineItemSchema),
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
					addDialogOpen = false;
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
		delayMs: 500,
		timeoutMs: 8000,
	});

	const editForm = superForm(data.editForm, {
		validators: zodClient(editLineItemSchema),
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
					editDialogOpen = false;
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
		delayMs: 500,
		timeoutMs: 8000,
	});

	const { form: formData, enhance } = form;
	const { form: editFormData, enhance: editEnhance } = editForm;

	function openAddDialog() {
		// Set next line number
		const maxLineNumber = Math.max(...lineItems.map((item) => item.line_number), 0);
		$formData.line_number = maxLineNumber + 1;
		addDialogOpen = true;
	}

	function openEditDialog(lineItem: (typeof lineItems)[0]) {
		selectedLineItem = lineItem;
		// Populate edit form with current values
		$editFormData.tender_line_item_id = lineItem.tender_line_item_id;
		$editFormData.line_number = lineItem.line_number;
		$editFormData.description = lineItem.description;
		$editFormData.quantity = lineItem.quantity;
		$editFormData.unit = lineItem.unit;
		$editFormData.material_rate = lineItem.material_rate;
		$editFormData.labor_rate = lineItem.labor_rate;
		$editFormData.productivity_factor = lineItem.productivity_factor;
		$editFormData.unit_rate = lineItem.unit_rate;
		$editFormData.subtotal = lineItem.subtotal;
		$editFormData.normalization_type = lineItem.normalization_type || 'amount';
		$editFormData.normalization_amount = lineItem.normalization_amount;
		$editFormData.normalization_percentage = lineItem.normalization_percentage;
		$editFormData.notes = lineItem.notes;
		editDialogOpen = true;
	}

	function openDeleteDialog(lineItem: (typeof lineItems)[0]) {
		selectedLineItem = lineItem;
		deleteDialogOpen = true;
	}

	// Calculate subtotal when quantity and unit_rate change
	function calculateSubtotal() {
		const quantity = $formData.quantity || 0;
		const unitRate = $formData.unit_rate || 0;
		if (quantity > 0 && unitRate > 0) {
			$formData.subtotal = quantity * unitRate;
		}
	}

	// Calculate unit rate from material rate, labor rate, and productivity
	function calculateUnitRate() {
		const materialRate = $formData.material_rate || 0;
		const laborRate = $formData.labor_rate || 0;
		const productivity = $formData.productivity_factor || 1;

		if (materialRate > 0 || laborRate > 0) {
			const calculatedRate = materialRate + laborRate / productivity;
			$formData.unit_rate = calculatedRate;
			calculateSubtotal();
		}
	}
</script>

<svelte:head>
	<title>Line Items - {tender?.tender_name} - {data.project?.name}</title>
</svelte:head>

<div class="space-y-6 p-4 lg:p-6">
	<!-- Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold tracking-tight">Line Items</h1>
			<p class="text-muted-foreground">
				Manage line items for {tender?.tender_name}
			</p>
		</div>
		<div class="flex items-center space-x-2">
			<Button onclick={openAddDialog}>
				<PlusIcon class="mr-2 h-4 w-4" />
				Add Line Item
			</Button>
		</div>
	</div>

	<!-- Summary Card -->
	<Card.Root>
		<Card.Header>
			<Card.Title>Summary</Card.Title>
		</Card.Header>
		<Card.Content>
			<dl class="grid grid-cols-3 gap-4">
				<div>
					<dt class="text-sm font-medium text-gray-500">Total Line Items</dt>
					<dd class="text-2xl font-bold">{lineItems.length}</dd>
				</div>
				<div>
					<dt class="text-sm font-medium text-gray-500">Total Amount</dt>
					<dd class="text-2xl font-bold">
						{formatCurrency(
							lineItems.reduce((sum, item) => sum + (item.subtotal || 0), 0),
							{
								symbol: tender.currency.symbol,
								symbolPosition: tender.currency.symbol_position as 'before' | 'after',
								fallback: '-',
							},
						)}
					</dd>
				</div>
				<div>
					<dt class="text-sm font-medium text-gray-500">WBS Mappings</dt>
					<dd class="text-2xl font-bold">
						{lineItems.reduce(
							(sum: number, item) => sum + (item.tender_wbs_mapping?.length || 0),
							0,
						)}
					</dd>
				</div>
			</dl>
		</Card.Content>
	</Card.Root>

	<!-- Line Items Table -->
	{#if lineItems.length > 0}
		<div class="rounded-md border">
			<Table>
				<TableHeader>
					<TableRow>
						<TableHead>Line #</TableHead>
						<TableHead>Description</TableHead>
						<TableHead>Quantity</TableHead>
						<TableHead>Unit</TableHead>
						<TableHead>Unit Rate</TableHead>
						<TableHead>Subtotal</TableHead>
						<TableHead>Normalization</TableHead>
						<TableHead>WBS</TableHead>
						<TableHead class="text-right">Actions</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{#each lineItems as item (item.tender_line_item_id)}
						<TableRow>
							<TableCell>{item.line_number}</TableCell>
							<TableCell class="max-w-xs">
								<div class="truncate" title={item.description}>
									{item.description}
								</div>
							</TableCell>
							<TableCell>{item.quantity || '-'}</TableCell>
							<TableCell>{item.unit || '-'}</TableCell>
							<TableCell>
								{item.unit_rate
									? formatCurrency(item.unit_rate, {
											symbol: tender.currency.symbol,
											symbolPosition: tender.currency.symbol_position as 'before' | 'after',
											fallback: '-',
										})
									: '-'}
							</TableCell>
							<TableCell>
								{item.subtotal
									? formatCurrency(item.subtotal, {
											symbol: tender.currency.symbol,
											symbolPosition: tender.currency.symbol_position as 'before' | 'after',
											fallback: '-',
										})
									: '-'}
							</TableCell>
							<TableCell>
								{#if item.normalization_type === 'percentage'}
									{item.normalization_percentage}%
								{:else if item.normalization_amount}
									{formatCurrency(item.normalization_amount, {
										symbol: tender.currency.symbol,
										symbolPosition: tender.currency.symbol_position as 'before' | 'after',
										fallback: '-',
									})}
								{:else}
									-
								{/if}
							</TableCell>
							<TableCell>
								{item.tender_wbs_mapping?.length || 0}
							</TableCell>
							<TableCell class="text-right">
								<DropdownMenu.Root>
									<DropdownMenu.Trigger>
										{#snippet child({ props })}
											<Button {...props} variant="ghost" class="size-8 p-0">
												<span class="sr-only">Open menu</span>
												<DotsThreeVerticalIcon class="size-4" />
											</Button>
										{/snippet}
									</DropdownMenu.Trigger>
									<DropdownMenu.Content align="end">
										<DropdownMenu.Item onclick={() => openEditDialog(item)}>
											<PencilIcon class="mr-2 h-4 w-4" />
											Edit
										</DropdownMenu.Item>
										<DropdownMenu.Item onclick={() => openDeleteDialog(item)}>
											<TrashIcon class="mr-2 h-4 w-4" />
											Delete
										</DropdownMenu.Item>
									</DropdownMenu.Content>
								</DropdownMenu.Root>
							</TableCell>
						</TableRow>
					{/each}
				</TableBody>
			</Table>
		</div>
	{:else}
		<div class="py-12 text-center">
			<h3 class="mt-2 text-sm font-semibold text-gray-900">No line items</h3>
			<p class="mt-1 text-sm text-gray-500">Get started by adding your first line item.</p>
			<div class="mt-6">
				<Button onclick={openAddDialog}>
					<PlusIcon class="mr-2 h-4 w-4" />
					Add Line Item
				</Button>
			</div>
		</div>
	{/if}
</div>

<!-- Add Line Item Dialog -->
<Dialog.Root bind:open={addDialogOpen}>
	<Dialog.Content class="max-w-4xl">
		<Dialog.Header>
			<Dialog.Title>Add Line Item</Dialog.Title>
			<Dialog.Description>Add a new line item to this tender revision.</Dialog.Description>
		</Dialog.Header>
		<form method="POST" action="?/create" use:enhance>
			<div class="grid grid-cols-2 gap-4 py-4">
				<Form.Field {form} name="line_number">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Line Number <span class="text-red-500">*</span></Form.Label>
							<Input {...props} type="number" bind:value={$formData.line_number} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field {form} name="unit">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Unit</Form.Label>
							<Input {...props} placeholder="e.g., m², kg, hours" bind:value={$formData.unit} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<div class="col-span-2">
					<Form.Field {form} name="description">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Description <span class="text-red-500">*</span></Form.Label>
								<Textarea {...props} bind:value={$formData.description} />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<Form.Field {form} name="quantity">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Quantity</Form.Label>
							<Input
								{...props}
								type="number"
								step="0.0001"
								bind:value={$formData.quantity}
								onchange={calculateSubtotal}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field {form} name="unit_rate">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Unit Rate</Form.Label>
							<Input
								{...props}
								type="number"
								step="0.01"
								bind:value={$formData.unit_rate}
								onchange={calculateSubtotal}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field {form} name="material_rate">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Material Rate</Form.Label>
							<Input
								{...props}
								type="number"
								step="0.01"
								bind:value={$formData.material_rate}
								onchange={calculateUnitRate}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field {form} name="labor_rate">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Labor Rate</Form.Label>
							<Input
								{...props}
								type="number"
								step="0.01"
								bind:value={$formData.labor_rate}
								onchange={calculateUnitRate}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field {form} name="productivity_factor">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Productivity</Form.Label>
							<Input
								{...props}
								type="number"
								step="0.01"
								bind:value={$formData.productivity_factor}
								onchange={calculateUnitRate}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field {form} name="subtotal">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Subtotal</Form.Label>
							<Input {...props} type="number" step="0.01" bind:value={$formData.subtotal} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
			</div>

			<!-- Normalization Input -->
			<div class="space-y-2">
				<span class="text-sm font-medium">Normalization</span>
				<NormalizationInput
					bind:type={$formData.normalization_type}
					amount={$formData.normalization_amount?.toString() || ''}
					percentage={$formData.normalization_percentage?.toString() || ''}
					subtotal={$formData.subtotal || 0}
					currencySymbol={tender.currency.symbol}
					symbolPosition={tender.currency.symbol_position as 'before' | 'after'}
					onTypeChange={(type) => ($formData.normalization_type = type)}
					onAmountChange={(amount) => ($formData.normalization_amount = parseFloat(amount) || null)}
					onPercentageChange={(percentage) =>
						($formData.normalization_percentage = parseFloat(percentage) || null)}
				/>
			</div>
			<Dialog.Footer>
				<Button type="button" variant="outline" onclick={() => (addDialogOpen = false)}>
					Cancel
				</Button>
				<Button type="submit">Add Line Item</Button>
			</Dialog.Footer>
		</form>
	</Dialog.Content>
</Dialog.Root>

<!-- Edit Line Item Dialog -->
<Dialog.Root bind:open={editDialogOpen}>
	<Dialog.Content class="max-w-4xl">
		<form method="POST" action="?/update" use:editEnhance class="space-y-4">
			<Dialog.Header>
				<Dialog.Title>Edit Line Item</Dialog.Title>
				<Dialog.Description>Update the line item details.</Dialog.Description>
			</Dialog.Header>
			<input
				type="hidden"
				name="tender_line_item_id"
				value={selectedLineItem?.tender_line_item_id}
			/>
			<div class="grid grid-cols-2 gap-4 py-4">
				<Form.Field form={editForm} name="line_number">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Line Number <span class="text-red-500">*</span></Form.Label>
							<Input {...props} type="number" bind:value={$editFormData.line_number} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field form={editForm} name="description">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Description <span class="text-red-500">*</span></Form.Label>
							<Textarea {...props} bind:value={$editFormData.description} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field form={editForm} name="quantity">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Quantity</Form.Label>
							<Input {...props} type="number" step="0.01" bind:value={$editFormData.quantity} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field form={editForm} name="unit">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Unit</Form.Label>
							<Input {...props} bind:value={$editFormData.unit} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field form={editForm} name="material_rate">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Material Rate</Form.Label>
							<Input
								{...props}
								type="number"
								step="0.01"
								bind:value={$editFormData.material_rate}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field form={editForm} name="labor_rate">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Labor Rate</Form.Label>
							<Input {...props} type="number" step="0.01" bind:value={$editFormData.labor_rate} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field form={editForm} name="productivity_factor">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Productivity Factor</Form.Label>
							<Input
								{...props}
								type="number"
								step="0.01"
								bind:value={$editFormData.productivity_factor}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field form={editForm} name="unit_rate">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Unit Rate</Form.Label>
							<Input {...props} type="number" step="0.01" bind:value={$editFormData.unit_rate} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field form={editForm} name="subtotal">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Subtotal</Form.Label>
							<Input {...props} type="number" step="0.01" bind:value={$editFormData.subtotal} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field form={editForm} name="notes">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Notes</Form.Label>
							<Textarea {...props} bind:value={$editFormData.notes} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
			</div>

			<!-- Normalization Input -->
			<div class="space-y-2">
				<span class="text-sm font-medium">Normalization</span>
				<NormalizationInput
					bind:type={$editFormData.normalization_type}
					amount={$editFormData.normalization_amount?.toString() || ''}
					percentage={$editFormData.normalization_percentage?.toString() || ''}
					subtotal={$editFormData.subtotal || 0}
					currencySymbol={tender.currency.symbol}
					symbolPosition={tender.currency.symbol_position as 'before' | 'after'}
					onTypeChange={(type) => ($editFormData.normalization_type = type)}
					onAmountChange={(amount) =>
						($editFormData.normalization_amount = parseFloat(amount) || null)}
					onPercentageChange={(percentage) =>
						($editFormData.normalization_percentage = parseFloat(percentage) || null)}
				/>
			</div>
			<Dialog.Footer>
				<Button type="button" variant="outline" onclick={() => (editDialogOpen = false)}>
					Cancel
				</Button>
				<Button type="submit">Update Line Item</Button>
			</Dialog.Footer>
		</form>
	</Dialog.Content>
</Dialog.Root>

<!-- Delete Confirmation Dialog -->
<ConfirmationDialog
	open={deleteDialogOpen}
	title="Delete Line Item"
	description="Are you sure you want to delete this line item? This action cannot be undone."
	confirmText="Delete"
	variant="destructive"
	onCancel={() => {
		deleteDialogOpen = false;
		selectedLineItem = null;
	}}
>
	{#snippet formContent()}
		<form
			method="POST"
			action="?/delete"
			use:svelteKitEnhance={() => {
				return ({ update }: { update: () => void }) => {
					deleteDialogOpen = false;
					selectedLineItem = null;
					update();
				};
			}}
		>
			<input type="hidden" name="line_item_id" value={selectedLineItem?.tender_line_item_id} />
			<div class="flex gap-3 pt-4">
				<Button
					variant="outline"
					type="button"
					onclick={() => {
						deleteDialogOpen = false;
						selectedLineItem = null;
					}}
				>
					Cancel
				</Button>
				<Button variant="destructive" type="submit">Delete</Button>
			</div>
		</form>
	{/snippet}
</ConfirmationDialog>
