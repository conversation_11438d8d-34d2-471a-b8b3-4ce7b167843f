import { error } from '@sveltejs/kit';
import type { SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '$lib/database.types';
import {
	type CreateTenderFormData,
	type EditTenderFormData,
	type CreateLineItemFormData,
	type EditLineItemFormData,
} from '$lib/schemas/tender';

/**
 * Creates a new tender
 */
export async function createTender(
	supabase: SupabaseClient<Database>,
	tenderData: CreateTenderFormData,
) {
	const { data, error: err } = await supabase
		.from('tender')
		.insert({
			project_id: tenderData.project_id,
			vendor_id: tenderData.vendor_id,
			tender_name: tenderData.tender_name,
			description: tenderData.description,
			submission_date: tenderData.submission_date,
			currency_code: tenderData.currency_code,
			status: tenderData.status,
			notes: tenderData.notes,
		})
		.select()
		.single();

	if (err) {
		throw error(500, {
			message: `Failed to create tender: ${err.message}`,
		});
	}

	// Create initial revision
	const { error: revisionErr } = await supabase.from('tender_revision').insert({
		tender_id: data.tender_id,
		revision_number: 1,
		is_current: true,
		revision_notes: 'Initial revision',
	});

	if (revisionErr) {
		throw error(500, {
			message: `Failed to create initial revision: ${revisionErr.message}`,
		});
	}

	return data;
}

/**
 * Updates an existing tender
 */
export async function updateTender(
	supabase: SupabaseClient<Database>,
	tenderId: string,
	tenderData: Partial<EditTenderFormData>,
) {
	const { data, error: err } = await supabase
		.from('tender')
		.update({
			tender_name: tenderData.tender_name,
			description: tenderData.description,
			submission_date: tenderData.submission_date || undefined,
			currency_code: tenderData.currency_code,
			status: tenderData.status,
			notes: tenderData.notes,
		})
		.eq('tender_id', tenderId)
		.select()
		.single();

	if (err) {
		throw error(500, {
			message: `Failed to update tender: ${err.message}`,
		});
	}

	return data;
}

/**
 * Deletes a tender and all related data
 */
export async function deleteTender(supabase: SupabaseClient<Database>, tenderId: string) {
	const { error: err } = await supabase.from('tender').delete().eq('tender_id', tenderId);

	if (err) {
		throw error(500, {
			message: `Failed to delete tender: ${err.message}`,
		});
	}

	return true;
}

/**
 * Gets a tender by ID with full details
 */
export async function getTenderById(supabase: SupabaseClient<Database>, tenderId: string) {
	const { data, error: err } = await supabase
		.from('tender')
		.select(
			`
			*,
			vendor:vendor_id (vendor_id, name, currency),
			currency:currency_code (currency_code, symbol, symbol_position, description),
			tender_revision!inner (
				*,
				tender_line_item (
					*,
					tender_wbs_mapping (
						*,
						wbs_library_item:wbs_library_item_id (code, description, level)
					)
				)
			),
			tender_score (
				*,
				tender_scoring_criteria:tender_scoring_criteria_id (*)
			)
		`,
		)
		.eq('tender_id', tenderId)
		.eq('tender_revision.is_current', true)
		.single();

	if (err) {
		throw error(500, {
			message: `Failed to get tender: ${err.message}`,
		});
	}

	return data;
}

/**
 * Creates a new tender revision
 */
export async function createTenderRevision(
	supabase: SupabaseClient<Database>,
	tenderId: string,
	revisionNotes?: string,
) {
	// Get current revision
	const { data: currentRevision, error: getCurrentErr } = await supabase
		.from('tender_revision')
		.select('revision_number, tender_revision_id')
		.eq('tender_id', tenderId)
		.eq('is_current', true)
		.single();

	if (getCurrentErr) {
		throw error(500, {
			message: `Failed to get current revision: ${getCurrentErr.message}`,
		});
	}

	if (!currentRevision?.tender_revision_id) {
		throw error(500, { message: 'Current revision could not be determined' });
	}

	const previousRevisionId = currentRevision.tender_revision_id;
	const newRevisionNumber = currentRevision.revision_number + 1;

	// First, deactivate the current revision
	const { error: deactivateErr } = await supabase
		.from('tender_revision')
		.update({ is_current: false })
		.eq('tender_revision_id', previousRevisionId);

	if (deactivateErr) {
		throw error(500, {
			message: `Failed to deactivate current revision: ${deactivateErr.message}`,
		});
	}

	// Then create new revision
	const { data, error: err } = await supabase
		.from('tender_revision')
		.insert({
			tender_id: tenderId,
			revision_number: newRevisionNumber,
			is_current: true,
			revision_notes: revisionNotes,
		})
		.select()
		.single();

	if (err) {
		// Attempt to restore previous current revision before throwing
		if (previousRevisionId) {
			await supabase
				.from('tender_revision')
				.update({ is_current: true })
				.eq('tender_revision_id', previousRevisionId);
		}
		throw error(500, {
			message: `Failed to create revision: ${err.message}`,
		});
	}

	return data;
}

/**
 * Creates a new line item for a tender revision
 */
export async function createLineItem(
	supabase: SupabaseClient<Database>,
	lineItemData: CreateLineItemFormData,
) {
	const { data, error: err } = await supabase
		.from('tender_line_item')
		.insert({
			tender_revision_id: lineItemData.tender_revision_id,
			line_number: lineItemData.line_number,
			description: lineItemData.description,
			quantity: lineItemData.quantity,
			unit: lineItemData.unit,
			material_rate: lineItemData.material_rate,
			labor_rate: lineItemData.labor_rate,
			productivity_factor: lineItemData.productivity_factor,
			unit_rate: lineItemData.unit_rate,
			subtotal: lineItemData.subtotal || 0,
			normalization_type: lineItemData.normalization_type,
			normalization_amount: lineItemData.normalization_amount,
			normalization_percentage: lineItemData.normalization_percentage,
			notes: lineItemData.notes,
		})
		.select()
		.single();

	if (err) {
		throw error(500, {
			message: `Failed to create line item: ${err.message}`,
		});
	}

	return data;
}

/**
 * Updates an existing line item
 */
export async function updateLineItem(
	supabase: SupabaseClient<Database>,
	lineItemId: string,
	lineItemData: Partial<EditLineItemFormData>,
) {
	const { data, error: err } = await supabase
		.from('tender_line_item')
		.update({
			description: lineItemData.description,
			quantity: lineItemData.quantity,
			unit: lineItemData.unit,
			material_rate: lineItemData.material_rate,
			labor_rate: lineItemData.labor_rate,
			productivity_factor: lineItemData.productivity_factor,
			unit_rate: lineItemData.unit_rate,
			subtotal: lineItemData.subtotal || 0,
			normalization_type: lineItemData.normalization_type,
			normalization_amount: lineItemData.normalization_amount,
			normalization_percentage: lineItemData.normalization_percentage,
			notes: lineItemData.notes,
		})
		.eq('tender_line_item_id', lineItemId)
		.select()
		.single();

	if (err) {
		throw error(500, {
			message: `Failed to update line item: ${err.message}`,
		});
	}

	return data;
}

/**
 * Deletes a line item
 */
export async function deleteLineItem(supabase: SupabaseClient<Database>, lineItemId: string) {
	const { error: err } = await supabase
		.from('tender_line_item')
		.delete()
		.eq('tender_line_item_id', lineItemId);

	if (err) {
		throw error(500, {
			message: `Failed to delete line item: ${err.message}`,
		});
	}

	return true;
}

/**
 * Creates WBS mappings for a line item
 */
export async function createWbsMapping(
	supabase: SupabaseClient<Database>,
	lineItemId: string,
	mappingData: {
		wbs_library_item_id: string;
		coverage_percentage?: number;
		coverage_quantity?: number | null;
		notes?: string | null;
	},
) {
	const { data, error: err } = await supabase
		.from('tender_wbs_mapping')
		.insert({
			tender_line_item_id: lineItemId,
			wbs_library_item_id: mappingData.wbs_library_item_id,
			coverage_percentage: mappingData.coverage_percentage,
			coverage_quantity: mappingData.coverage_quantity,
			notes: mappingData.notes,
		})
		.select()
		.single();

	if (err) {
		throw error(500, {
			message: `Failed to create WBS mapping: ${err.message}`,
		});
	}

	return data;
}

/**
 * Updates normalization amount for a line item
 */
export async function updateNormalizationAmount(
	supabase: SupabaseClient<Database>,
	lineItemId: string,
	normalizationType: 'amount' | 'percentage',
	normalizationValue: number,
	calculatedAmount?: number | null,
) {
	const updateData: {
		normalization_type: 'amount' | 'percentage';
		normalization_amount?: number | null;
		normalization_percentage?: number | null;
	} = {
		normalization_type: normalizationType,
	};

	if (normalizationType === 'amount') {
		updateData.normalization_amount = normalizationValue;
		updateData.normalization_percentage = null;
	} else {
		// Store the percentage value in the percentage column
		updateData.normalization_percentage = normalizationValue;
		// Store the calculated amount in the amount column for reference
		updateData.normalization_amount = calculatedAmount;
	}

	const { data, error: err } = await supabase
		.from('tender_line_item')
		.update(updateData)
		.eq('tender_line_item_id', lineItemId)
		.select()
		.single();

	if (err) {
		throw error(500, {
			message: `Failed to update normalization: ${err.message}`,
		});
	}

	return data;
}

/**
 * Gets project tenders using RPC function
 */
export async function getProjectTenders(
	supabase: SupabaseClient<Database>,
	projectId: string,
	options?: {
		statusFilter?: Database['public']['Enums']['tender_status'];
		vendorFilter?: string;
		limit?: number;
		offset?: number;
	},
) {
	const { data, error: err } = await supabase.rpc('get_project_tenders', {
		project_id_param: projectId,
		status_filter: options?.statusFilter,
		vendor_filter: options?.vendorFilter,
		limit_param: options?.limit,
		offset_param: options?.offset,
	});

	if (err) {
		throw error(500, {
			message: `Failed to get project tenders: ${err.message}`,
		});
	}

	return data;
}

/**
 * Gets tender comparison data using RPC function
 */
export async function getTenderComparisonData(
	supabase: SupabaseClient<Database>,
	projectId: string,
	options?: {
		statusFilter?: Database['public']['Enums']['tender_status'][];
		tenderIds?: string[];
	},
) {
	const { data, error: err } = await supabase.rpc('get_tender_comparison_data', {
		project_id_param: projectId,
		status_filter: options?.statusFilter,
		tender_ids: options?.tenderIds,
	});

	if (err) {
		throw error(500, {
			message: `Failed to get tender comparison data: ${err.message}`,
		});
	}

	return data;
}

/**
 * Calculates normalization amount using RPC function
 */
export async function calculateNormalizationAmount(
	supabase: SupabaseClient<Database>,
	lineItemId: string,
	normalizationPercentage: number,
) {
	const { data, error: err } = await supabase.rpc('calculate_normalization_amount', {
		tender_line_item_id_param: lineItemId,
		normalization_percentage_param: normalizationPercentage,
	});

	if (err) {
		throw error(500, {
			message: `Failed to calculate normalization amount: ${err.message}`,
		});
	}

	return data;
}

/**
 * Creates scoring criteria for a project
 */
export async function createScoringCriteria(
	supabase: SupabaseClient<Database>,
	projectId: string,
	criteriaData: {
		criteria_name: string;
		description?: string | null;
		weight?: number;
		max_score?: number;
		is_active?: boolean;
	},
) {
	const { data, error: err } = await supabase
		.from('tender_scoring_criteria')
		.insert({
			project_id: projectId,
			criteria_name: criteriaData.criteria_name,
			description: criteriaData.description,
			weight: criteriaData.weight || 1,
			max_score: criteriaData.max_score || 10,
			is_active: criteriaData.is_active ?? true,
		})
		.select()
		.single();

	if (err) {
		throw error(500, {
			message: `Failed to create scoring criteria: ${err.message}`,
		});
	}

	return data;
}

/**
 * Creates or updates tender scores
 */
export async function upsertTenderScore(
	supabase: SupabaseClient<Database>,
	tenderId: string,
	scoreData: {
		tender_scoring_criteria_id: string;
		score: number;
		comments?: string | null;
	},
) {
	const { data, error: err } = await supabase
		.from('tender_score')
		.upsert({
			tender_id: tenderId,
			tender_scoring_criteria_id: scoreData.tender_scoring_criteria_id,
			score: scoreData.score,
			comments: scoreData.comments,
		})
		.select()
		.single();

	if (err) {
		throw error(500, {
			message: `Failed to upsert tender score: ${err.message}`,
		});
	}

	return data;
}

/**
 * Creates a budget transfer
 */
export async function createBudgetTransfer(
	supabase: SupabaseClient<Database>,
	projectId: string,
	transferData: {
		tender_line_item_id?: string;
		from_wbs_library_item_id: string;
		to_wbs_library_item_id: string;
		transfer_amount: number;
		transfer_reason?: string | null;
	},
) {
	const { data, error: err } = await supabase
		.from('budget_transfer')
		.insert({
			project_id: projectId,
			tender_line_item_id: transferData.tender_line_item_id,
			from_wbs_library_item_id: transferData.from_wbs_library_item_id,
			to_wbs_library_item_id: transferData.to_wbs_library_item_id,
			transfer_amount: transferData.transfer_amount,
			reason: transferData.transfer_reason || '',
		})
		.select()
		.single();

	if (err) {
		throw error(500, {
			message: `Failed to create budget transfer: ${err.message}`,
		});
	}

	return data;
}

/**
 * Gets all revisions for a tender
 */
export async function getTenderRevisions(supabase: SupabaseClient<Database>, tenderId: string) {
	const { data, error: err } = await supabase
		.from('tender_revision')
		.select('*')
		.eq('tender_id', tenderId)
		.order('revision_number', { ascending: false });

	if (err) {
		throw error(500, {
			message: `Failed to get tender revisions: ${err.message}`,
		});
	}

	return data;
}

/**
 * Sets a specific revision as current
 */
export async function setCurrentRevision(supabase: SupabaseClient<Database>, revisionId: string) {
	const { data, error: err } = await supabase
		.from('tender_revision')
		.update({ is_current: true })
		.eq('tender_revision_id', revisionId)
		.select()
		.single();

	if (err) {
		throw error(500, {
			message: `Failed to set current revision: ${err.message}`,
		});
	}

	return data;
}

/**
 * Gets the current revision for a tender
 */
export async function getCurrentRevision(supabase: SupabaseClient<Database>, tenderId: string) {
	const { data, error: err } = await supabase
		.from('tender_revision')
		.select('*')
		.eq('tender_id', tenderId)
		.eq('is_current', true)
		.single();

	if (err) {
		throw error(500, {
			message: `Failed to get current revision: ${err.message}`,
		});
	}

	return data;
}

/**
 * Bulk creates line items for a tender revision
 */
export async function bulkCreateLineItems(
	supabase: SupabaseClient<Database>,
	revisionId: string,
	lineItems: Omit<CreateLineItemFormData, 'tender_revision_id'>[],
) {
	const lineItemsWithRevision = lineItems.map((item) => ({
		...item,
		tender_revision_id: revisionId,
		subtotal: item.subtotal || 0,
	}));

	const { data, error: err } = await supabase
		.from('tender_line_item')
		.insert(lineItemsWithRevision)
		.select();

	if (err) {
		throw error(500, {
			message: `Failed to bulk create line items: ${err.message}`,
		});
	}

	return data;
}

/**
 * Gets line items for a tender revision with WBS mappings
 */
export async function getLineItemsWithMappings(
	supabase: SupabaseClient<Database>,
	revisionId: string,
) {
	const { data, error: err } = await supabase
		.from('tender_line_item')
		.select(
			`
			*,
			tender_wbs_mapping (
				*,
				wbs_library_item:wbs_library_item_id (code, description, level)
			)
		`,
		)
		.eq('tender_revision_id', revisionId)
		.order('line_number');

	if (err) {
		throw error(500, {
			message: `Failed to get line items with mappings: ${err.message}`,
		});
	}

	return data;
}

/**
 * Updates line item subtotal based on quantity and unit rate
 */
export async function updateLineItemSubtotal(
	supabase: SupabaseClient<Database>,
	lineItemId: string,
	quantity?: number,
	unitRate?: number,
) {
	let subtotal: number | null = null;
	if (quantity && unitRate) {
		subtotal = quantity * unitRate;
	}

	const { data, error: err } = await supabase
		.from('tender_line_item')
		.update({ subtotal: subtotal || 0 })
		.eq('tender_line_item_id', lineItemId)
		.select()
		.single();

	if (err) {
		throw error(500, {
			message: `Failed to update line item subtotal: ${err.message}`,
		});
	}

	return data;
}

/**
 * Validates normalization input based on type
 */
export function validateNormalizationInput(
	normalizationType: 'amount' | 'percentage',
	normalizationAmount?: number | null,
	normalizationPercentage?: number | null,
): { isValid: boolean; error?: string } {
	if (normalizationType === 'amount') {
		if (normalizationAmount === null || normalizationAmount === undefined) {
			return { isValid: false, error: 'Normalization amount is required when type is amount' };
		}
		if (normalizationAmount < 0) {
			return { isValid: false, error: 'Normalization amount must be positive' };
		}
	} else if (normalizationType === 'percentage') {
		if (normalizationPercentage === null || normalizationPercentage === undefined) {
			return {
				isValid: false,
				error: 'Normalization percentage is required when type is percentage',
			};
		}
		if (normalizationPercentage < 0 || normalizationPercentage > 100) {
			return { isValid: false, error: 'Normalization percentage must be between 0 and 100' };
		}
	}

	return { isValid: true };
}

/**
 * Calculates unit rate from material rate, labor rate, and productivity
 */
export function calculateUnitRate(
	materialRate?: number | null,
	laborRate?: number | null,
	productivity?: number | null,
): number | null {
	if (!materialRate && !laborRate) {
		return null;
	}

	let unitRate = 0;

	if (materialRate) {
		unitRate += materialRate;
	}

	if (laborRate && productivity) {
		unitRate += laborRate / productivity;
	}

	return unitRate;
}

/**
 * Updates line item with calculated values
 */
export async function updateLineItemWithCalculations(
	supabase: SupabaseClient<Database>,
	lineItemId: string,
	updates: Partial<EditLineItemFormData>,
) {
	// Calculate unit rate if material/labor rates are provided
	const unitRate = calculateUnitRate(
		updates.material_rate,
		updates.labor_rate,
		updates.productivity_factor,
	);

	// Calculate subtotal if quantity and unit rate are available
	let subtotal: number | null = null;
	if (updates.quantity && unitRate) {
		subtotal = updates.quantity * unitRate;
	}

	const updateData = {
		...updates,
		unit_rate: unitRate,
		subtotal: subtotal || 0,
	};

	const { data, error: err } = await supabase
		.from('tender_line_item')
		.update(updateData)
		.eq('tender_line_item_id', lineItemId)
		.select()
		.single();

	if (err) {
		throw error(500, {
			message: `Failed to update line item with calculations: ${err.message}`,
		});
	}

	return data;
}
